from pyrogram import Client
import asyncio

# Telegram API credentials
api_id = "********"  # Your Telegram API ID
api_hash = "781cb964bc326312b9c77f4c17ddf491"  # Your Telegram API Hash
phone_number = "************"  # Your phone number

# Initialize the Pyrogram Client
app = Client("my_account", api_id=api_id, api_hash=api_hash, phone_number=phone_number)

async def get_auto_folder_chats():
    # List to store chat IDs (manually filter for "AUTO" folder)
    auto_chats = []
    
    # Iterate through all dialogs
    async for dialog in app.get_dialogs():
        chat = dialog.chat
        # Placeholder: Manually filter chats (e.g., by title or known IDs)
        # Replace this with your logic to identify "AUTO" folder chats
        if "AUTO" in (chat.title or chat.first_name or "").upper():
            auto_chats.append(chat.id)
            print(f"Found chat (possible AUTO folder): {chat.title or chat.first_name} (ID: {chat.id})")
    
    if not auto_chats:
        print("No chats found that match the 'AUTO' criteria.")
    return auto_chats

async def send_message_to_auto_chats():
    # Get chat IDs for the "AUTO" folder
    chat_ids = await get_auto_folder_chats()
    
    if not chat_ids:
        print("No chats found to send messages to.")
        return
    
    # Message to send
    message_text = "Salom! Bu AUTO jildidagi barcha chatlarga yuborilgan xabar."
    
    # Send message to each chat
    for chat_id in chat_ids:
        try:
            await app.send_message(chat_id=chat_id, text=message_text)
            print(f"Xabar {chat_id} ga yuborildi.")
            await asyncio.sleep(1)  # Pause to respect Telegram API limits
        except Exception as e:
            print(f"Xabar yuborishda xato (Chat ID: {chat_id}): {e}")

# Main function to manage the client lifecycle
async def main():
    async with app:
        await send_message_to_auto_chats()

# Run the script
if __name__ == "__main__":
    app.run(main())
