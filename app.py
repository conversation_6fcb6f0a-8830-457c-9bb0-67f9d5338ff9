#!/usr/bin/env python3
"""
Telethon-based app for sending messages to AUTO folder chats
Integrates with Django models and session management
"""
import os
import django
import asyncio
import logging
from typing import List, Optional

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from telethon import TelegramClient, types
from telethon.tl.functions.messages import GetDialogFiltersRequest
from telethon.tl.functions.messages import SendMessageRequest
from telethon.errors import FloodWaitError, SessionPasswordNeededError
from userbot.models import Session

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TelethonAutoFolderBot:
    """
    Telethon-based bot for working with AUTO folder chats
    """

    def __init__(self, session_name: str = None):
        self.session_name = session_name
        self.client = None
        self.session_data = None

    async def initialize_from_db(self, session_name: str = None) -> bool:
        """
        Initialize client using session data from Django database
        """
        try:
            if session_name:
                self.session_name = session_name

            if not self.session_name:
                # Get first active session
                active_session = Session.objects.filter(is_active=True).first()
                if not active_session:
                    logger.error("No active sessions found in database")
                    return False
                self.session_name = active_session.session_name

            # Get session data from database
            self.session_data = Session.objects.get(session_name=self.session_name)

            # Initialize Telethon client
            self.client = TelegramClient(
                self.session_name,
                int(self.session_data.api_id),
                self.session_data.api_hash,
                connection_retries=3,
                retry_delay=1,
                timeout=30
            )

            logger.info(f"Initialized client for session: {self.session_name}")
            return True

        except Session.DoesNotExist:
            logger.error(f"Session '{self.session_name}' not found in database")
            return False
        except Exception as e:
            logger.error(f"Error initializing client: {e}")
            return False

    async def connect(self) -> bool:
        """
        Connect to Telegram and check authorization
        """
        try:
            await self.client.connect()

            if not await self.client.is_user_authorized():
                logger.error(f"Session {self.session_name} is not authorized")
                return False

            me = await self.client.get_me()
            logger.info(f"Connected as: {me.first_name} (@{me.username})")
            return True

        except Exception as e:
            logger.error(f"Error connecting: {e}")
            return False

    async def get_dialog_filters(self) -> List:
        """
        Get all dialog filters (folders) from Telegram
        """
        try:
            result = await self.client(GetDialogFiltersRequest())
            return result.filters
        except Exception as e:
            logger.error(f"Error getting dialog filters: {e}")
            return []

    async def get_auto_folder_chats(self) -> List[int]:
        """
        Get chat IDs from AUTO folder using Telegram's folder system
        """
        auto_chats = []

        try:
            # Get dialog filters (folders)
            filters = await self.get_dialog_filters()

            auto_filter = None
            for filter_obj in filters:
                if hasattr(filter_obj, 'title') and 'AUTO' in filter_obj.title.upper():
                    auto_filter = filter_obj
                    logger.info(f"Found AUTO folder: {filter_obj.title}")
                    break

            if auto_filter and hasattr(auto_filter, 'include_peers'):
                # Get chats from the AUTO folder
                for peer in auto_filter.include_peers:
                    if isinstance(peer, types.InputPeerChat):
                        auto_chats.append(-peer.chat_id)  # Group chat ID
                    elif isinstance(peer, types.InputPeerChannel):
                        auto_chats.append(-1000000000000 - peer.channel_id)  # Channel/supergroup ID
                    elif isinstance(peer, types.InputPeerUser):
                        auto_chats.append(peer.user_id)  # User ID

                logger.info(f"Found {len(auto_chats)} chats in AUTO folder")
            else:
                logger.warning("AUTO folder not found, falling back to title-based search")
                # Fallback: search by chat titles containing "AUTO"
                auto_chats = await self.get_auto_chats_by_title()

        except Exception as e:
            logger.error(f"Error getting AUTO folder chats: {e}")
            # Fallback to title-based search
            auto_chats = await self.get_auto_chats_by_title()

        return auto_chats

    async def get_auto_chats_by_title(self) -> List[int]:
        """
        Fallback method: find chats with 'AUTO' in title
        """
        auto_chats = []

        try:
            async for dialog in self.client.iter_dialogs():
                chat_title = ""
                if hasattr(dialog.entity, 'title'):
                    chat_title = dialog.entity.title or ""
                elif hasattr(dialog.entity, 'first_name'):
                    chat_title = dialog.entity.first_name or ""

                if 'AUTO' in chat_title.upper():
                    auto_chats.append(dialog.entity.id)
                    logger.info(f"Found AUTO chat by title: {chat_title} (ID: {dialog.entity.id})")

        except Exception as e:
            logger.error(f"Error in title-based search: {e}")

        return auto_chats

    async def send_message_to_chats(self, chat_ids: List[int], message_text: str) -> dict:
        """
        Send message to multiple chats with error handling and rate limiting
        """
        results = {
            'success': [],
            'failed': [],
            'total': len(chat_ids)
        }

        if not chat_ids:
            logger.warning("No chat IDs provided")
            return results

        logger.info(f"Sending message to {len(chat_ids)} chats")

        for i, chat_id in enumerate(chat_ids, 1):
            try:
                # Send message using Telethon
                await self.client.send_message(chat_id, message_text)
                results['success'].append(chat_id)
                logger.info(f"[{i}/{len(chat_ids)}] Message sent to chat {chat_id}")

                # Rate limiting - wait between messages
                if i < len(chat_ids):  # Don't wait after the last message
                    await asyncio.sleep(1)

            except FloodWaitError as e:
                logger.warning(f"Flood wait error for chat {chat_id}: waiting {e.seconds} seconds")
                await asyncio.sleep(e.seconds)
                # Retry after flood wait
                try:
                    await self.client.send_message(chat_id, message_text)
                    results['success'].append(chat_id)
                    logger.info(f"[{i}/{len(chat_ids)}] Message sent to chat {chat_id} (after flood wait)")
                except Exception as retry_error:
                    results['failed'].append({'chat_id': chat_id, 'error': str(retry_error)})
                    logger.error(f"Failed to send message to chat {chat_id} after retry: {retry_error}")

            except Exception as e:
                results['failed'].append({'chat_id': chat_id, 'error': str(e)})
                logger.error(f"Failed to send message to chat {chat_id}: {e}")

        logger.info(f"Message sending completed. Success: {len(results['success'])}, Failed: {len(results['failed'])}")
        return results

    async def send_message_to_auto_chats(self, message_text: str = None) -> dict:
        """
        Main method to send message to all AUTO folder chats
        """
        if not message_text:
            message_text = "Salom! Bu AUTO jildidagi barcha chatlarga yuborilgan xabar."

        logger.info("Starting to send messages to AUTO folder chats")

        # Get AUTO folder chat IDs
        chat_ids = await self.get_auto_folder_chats()

        if not chat_ids:
            logger.warning("No chats found in AUTO folder")
            return {'success': [], 'failed': [], 'total': 0}

        # Send messages
        results = await self.send_message_to_chats(chat_ids, message_text)
        return results

    async def disconnect(self):
        """
        Disconnect from Telegram
        """
        if self.client and self.client.is_connected():
            await self.client.disconnect()
            logger.info("Disconnected from Telegram")


async def main():
    """
    Main function to run the AUTO folder message sender
    """
    bot = TelethonAutoFolderBot()

    try:
        # Initialize from database
        if not await bot.initialize_from_db():
            logger.error("Failed to initialize bot from database")
            return

        # Connect to Telegram
        if not await bot.connect():
            logger.error("Failed to connect to Telegram")
            return

        # Send messages to AUTO folder chats
        message_text = "Salom! Bu AUTO jildidagi barcha chatlarga yuborilgan xabar."
        results = await bot.send_message_to_auto_chats(message_text)

        # Print results
        print(f"\n{'='*50}")
        print(f"MESSAGE SENDING RESULTS")
        print(f"{'='*50}")
        print(f"Total chats: {results['total']}")
        print(f"Successful: {len(results['success'])}")
        print(f"Failed: {len(results['failed'])}")

        if results['success']:
            print(f"\nSuccessful chat IDs: {results['success']}")

        if results['failed']:
            print(f"\nFailed chats:")
            for failed in results['failed']:
                print(f"  Chat ID {failed['chat_id']}: {failed['error']}")

    except KeyboardInterrupt:
        logger.info("Script interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await bot.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
