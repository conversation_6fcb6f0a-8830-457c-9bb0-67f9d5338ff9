#!/usr/bin/env python3
"""
Simple script to run the AUTO folder message sender
"""
import asyncio
import sys
from app import TelethonAutoFolderBot


async def send_custom_message():
    """
    Send a custom message to AUTO folder chats
    """
    # Get message from command line argument or use default
    if len(sys.argv) > 1:
        message_text = " ".join(sys.argv[1:])
    else:
        message_text = input("Enter message to send (or press Enter for default): ").strip()
        if not message_text:
            message_text = "Salom! Bu AUTO jildidagi barcha chatlarga yuborilgan xabar."
    
    print(f"Message to send: {message_text}")
    print("-" * 50)
    
    bot = TelethonAutoFolderBot()
    
    try:
        # Initialize and connect
        if not await bot.initialize_from_db():
            print("❌ Failed to initialize bot from database")
            return
        
        if not await bot.connect():
            print("❌ Failed to connect to Telegram")
            return
        
        print("✅ Connected to Telegram successfully")
        
        # Get AUTO folder chats first
        print("🔍 Looking for AUTO folder chats...")
        chat_ids = await bot.get_auto_folder_chats()
        
        if not chat_ids:
            print("❌ No chats found in AUTO folder")
            return
        
        print(f"📁 Found {len(chat_ids)} chats in AUTO folder")
        print(f"Chat IDs: {chat_ids}")
        
        # Confirm before sending
        confirm = input(f"\nSend message to {len(chat_ids)} chats? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ Message sending cancelled")
            return
        
        # Send messages
        print("📤 Sending messages...")
        results = await bot.send_message_to_chats(chat_ids, message_text)
        
        # Show results
        print(f"\n{'='*50}")
        print(f"📊 RESULTS")
        print(f"{'='*50}")
        print(f"✅ Successful: {len(results['success'])}")
        print(f"❌ Failed: {len(results['failed'])}")
        print(f"📊 Total: {results['total']}")
        
        if results['failed']:
            print(f"\n❌ Failed chats:")
            for failed in results['failed']:
                print(f"  • Chat {failed['chat_id']}: {failed['error']}")
        
    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await bot.disconnect()


async def list_auto_chats():
    """
    List all chats in AUTO folder without sending messages
    """
    bot = TelethonAutoFolderBot()
    
    try:
        if not await bot.initialize_from_db():
            print("❌ Failed to initialize bot")
            return
        
        if not await bot.connect():
            print("❌ Failed to connect")
            return
        
        print("🔍 Getting AUTO folder chats...")
        chat_ids = await bot.get_auto_folder_chats()
        
        if not chat_ids:
            print("❌ No chats found in AUTO folder")
            return
        
        print(f"📁 Found {len(chat_ids)} chats in AUTO folder:")
        for i, chat_id in enumerate(chat_ids, 1):
            try:
                entity = await bot.client.get_entity(chat_id)
                name = getattr(entity, 'title', None) or getattr(entity, 'first_name', 'Unknown')
                print(f"  {i}. {name} (ID: {chat_id})")
            except Exception as e:
                print(f"  {i}. Chat ID: {chat_id} (Error getting name: {e})")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await bot.disconnect()


def main():
    """
    Main function with command line interface
    """
    if len(sys.argv) > 1 and sys.argv[1] == '--list':
        print("📋 Listing AUTO folder chats...")
        asyncio.run(list_auto_chats())
    else:
        print("📤 AUTO Folder Message Sender")
        print("Usage:")
        print("  python run_auto_sender.py                    # Interactive mode")
        print("  python run_auto_sender.py 'Your message'     # Send specific message")
        print("  python run_auto_sender.py --list             # List AUTO folder chats")
        print()
        
        if len(sys.argv) == 1:
            # Interactive mode
            asyncio.run(send_custom_message())
        elif sys.argv[1] != '--list':
            # Direct message mode
            asyncio.run(send_custom_message())


if __name__ == "__main__":
    main()
